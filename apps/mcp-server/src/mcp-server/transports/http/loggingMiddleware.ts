import { MiddlewareHandler } from 'hono';
import { logger, requestContextService } from '../../../utils/index.js';

export const loggingMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const context = requestContextService.createRequestContext({
      operation: 'loggingMiddleware',
      method: c.req.method,
      url: c.req.url,
    });

    let requestBody: any = null;

    // Only try to parse JSON for POST requests with JSON content type
    const contentType = c.req.header('content-type');
    if (c.req.method === 'POST' && contentType?.includes('application/json')) {
      try {
        requestBody = await c.req.json();
      } catch (error) {
        logger.warning('Failed to parse request body as JSON', {
          ...context,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    logger.info('Incoming HTTP Request', {
      ...context,
      headers: c.req.header(),
      body: requestBody,
    });

    await next();

    logger.info('Outgoing HTTP Response', {
        ...context,
        status: c.res.status,
        headers: c.res.headers,
    });
  };
};
