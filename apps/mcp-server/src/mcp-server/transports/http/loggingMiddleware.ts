import { MiddlewareHandler } from 'hono';
import { logger, requestContextService } from '../../../utils/index.js';

export const loggingMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const context = requestContextService.createRequestContext({
      operation: 'loggingMiddleware',
      method: c.req.method,
      url: c.req.url,
    });

    const requestBody = await c.req.json();

    logger.info('Incoming MCP Request', {
      ...context,
      headers: c.req.header(),
      body: requestBody,
    });

    await next();

    logger.info('Outgoing MCP Response', {
        ...context,
        status: c.res.status,
        headers: c.res.headers,
    });
  };
};
