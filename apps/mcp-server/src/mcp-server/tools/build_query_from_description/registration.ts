import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import {
  buildQueryFromDescriptionInputSchema,
  buildQueryFromDescriptionOutputSchema,
  buildQueryFromDescriptionLogic,
} from './logic.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/internal/errorHandler.js';
import { DataContract } from 'data-contract';
import { z } from 'zod';
import type { JSONSchema7 } from 'json-schema';

export function registerBuildQueryFromDescription(server: McpServer, contract: DataContract) {
  const inputJsonSchema = z.toJSONSchema(buildQueryFromDescriptionInputSchema) as JSONSchema7;
  const outputJsonSchema = z.toJSONSchema(buildQueryFromDescriptionOutputSchema) as JSONSchema7;

  server.registerTool(
    'build_query_from_description',
    {
      title: 'Build Query From Description',
      description: 'Builds a SQL query from a natural language description.',
      inputSchema: inputJsonSchema as any,
      outputSchema: outputJsonSchema as any,
    },
    async (params, callContext) => {
      try {
        const parsed = buildQueryFromDescriptionInputSchema.parse(params);

        const result = await buildQueryFromDescriptionLogic(contract, parsed);

        const validatedOutput = buildQueryFromDescriptionOutputSchema.parse(result);

        // NFR3: Log the final, executed SQL query
        console.log({
          message: 'Generated SQL Query',
          requestId: callContext.requestId?.toString(),
          sql: validatedOutput.sql_query,
          params: validatedOutput.params,
        });

        return {
          content: [{ type: 'text' as const, text: `Generated SQL: ${validatedOutput.sql_query}` }],
          structuredContent: validatedOutput,
        };
      } catch (err) {
        const handledError = ErrorHandler.handleError(err, {
          operation: 'build_query_from_description',
          context: {
            ...callContext,
            requestId: callContext.requestId?.toString(),
          },
          input: params,
        });
        return {
          isError: true,
          content: [{ type: 'text' as const, text: `Error: ${handledError.message}` }],
          structuredContent: { error: handledError.message },
        };
      }
    }
  );
}
