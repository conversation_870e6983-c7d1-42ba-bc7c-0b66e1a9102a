import { readDataContract, DataContract } from 'data-contract';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import redis from './redis.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DATA_CONTRACT_CACHE_KEY = 'mcp-server:data-contract';
const DATA_CONTRACT_CACHE_TTL_SECONDS = 3600; // 1 hour

// Timeout wrapper to prevent indefinite blocking
function withTimeout<T>(promise: Promise<T>, timeoutMs: number, operation: string): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error(`${operation} timed out after ${timeoutMs}ms`)), timeoutMs)
    )
  ]);
}

export async function getDataContract(): Promise<DataContract> {
  let dataContract: DataContract;

  try {
    console.log('Loading data contract...');

    // Add timeout to Redis operations to prevent blocking
    const cachedContract = await withTimeout(
      redis.get(DATA_CONTRACT_CACHE_KEY),
      5000,
      'Redis cache lookup'
    );

    if (cachedContract) {
      console.log('Data contract loaded from cache.');
      try {
        dataContract = JSON.parse(cachedContract) as DataContract;
        console.log('Data contract parsed successfully from cache.');
      } catch (parseError) {
        console.warn('Failed to parse cached data contract, reading from file.', parseError);
        // Clear invalid cache and read from file
        try {
          await withTimeout(redis.del(DATA_CONTRACT_CACHE_KEY), 2000, 'Redis cache clear');
        } catch (delError) {
          console.warn('Failed to clear invalid cache, continuing...', delError);
        }
        dataContract = await loadDataContractFromFile();
      }
    } else {
      console.log('Data contract cache miss. Reading from file.');
      dataContract = await loadDataContractFromFile();
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'test' || process.env.JEST_WORKER_ID) {
      // Provide a fallback for testing
      console.log('Using fallback data contract for testing environment.');
      dataContract = { tables: {}, abbreviations: {}, tools: {} };
    } else {
      console.error('FATAL: Could not load data contract from cache or file.', error);
      // Don't exit immediately, provide a fallback to keep the server running
      console.log('Using empty fallback data contract to keep server running.');
      dataContract = { tables: {}, abbreviations: {}, tools: {} };
    }
  }
  console.log('Data contract loading completed.');
  return dataContract;
}

async function loadDataContractFromFile(): Promise<DataContract> {
  const possiblePaths = [
    // Docker container path (production) - workspace structure
    path.resolve(process.cwd(), '../../packages/data-contract/datacontract.yml'),
    // Development paths
    path.resolve(__dirname, '../../../../packages/data-contract/datacontract.yml'),
    path.resolve(__dirname, '../../../packages/data-contract/datacontract.yml'),
    path.resolve(process.cwd(), 'packages/data-contract/datacontract.yml'),
    // Legacy Docker container path
    path.resolve(process.cwd(), 'node_modules/data-contract/datacontract.yml')
  ];

  console.log('Searching for data contract file in paths:', possiblePaths);
  const dataContractPath = possiblePaths.find(p => {
    const exists = fs.existsSync(p);
    console.log(`Checking path ${p}: ${exists ? 'found' : 'not found'}`);
    return exists;
  });

  if (!dataContractPath) {
    throw new Error(`Data contract file not found. Tried paths: ${possiblePaths.join(', ')}`);
  }

  console.log(`Reading data contract from: ${dataContractPath}`);
  const contractFromFile = readDataContract(dataContractPath);

  try {
    await withTimeout(
      redis.set(
        DATA_CONTRACT_CACHE_KEY,
        JSON.stringify(contractFromFile),
        'EX',
        DATA_CONTRACT_CACHE_TTL_SECONDS
      ),
      3000,
      'Redis cache set'
    );
    console.log('Data contract cached successfully.');
  } catch (cacheError) {
    console.warn('Failed to cache data contract, continuing without cache.', cacheError);
  }

  return contractFromFile;
}
