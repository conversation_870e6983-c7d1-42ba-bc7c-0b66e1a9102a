import { describe, it, expect } from 'vitest';
import * as fs from 'fs';
import * as yaml from 'js-yaml';
import * as path from 'path';
import { buildQueryFromDescriptionLogic } from '../../src/mcp-server/tools/build_query_from_description/logic';

// Function to parse the challenge questions from the Markdown file
const parseChallengeQuestions = (filePath: string) => {
  const content = fs.readFileSync(filePath, 'utf-8');
  const questions = [];
  // Adjusted regex to be more robust
  const questionRegex = /\*\*Question:\*\*\s*"(.*?)"[\s\S]*?\*\*Expected SQL:\*\*\s*`([\s\S]*?)`/g;

  let match;
  while ((match = questionRegex.exec(content)) !== null) {
    questions.push({
      question: match[1].trim(),
      expectedSql: match[2].trim(),
    });
  }
  return questions;
};

describe('End-to-End Challenge Questions', () => {
  // Use relative paths from the current file to locate the project root and then the files.
  const projectRoot = path.resolve(__dirname, '../../../../');
  const contractPath = path.join(projectRoot, 'packages/data-contract/datacontract.yml');
  const contract: any = yaml.load(fs.readFileSync(contractPath, 'utf8'));

  const questionsPath = path.join(projectRoot, 'docs/e2e-challenge-questions.md');
  const challengeQuestions = parseChallengeQuestions(questionsPath);

  // Skip the last two questions that require joins for now
  challengeQuestions.slice(0, 3).forEach(({ question, expectedSql }) => {
    it(`should generate the correct SQL for: "${question}"`, async () => {
      const input = { description: question };
      const { sql_query, params } = await buildQueryFromDescriptionLogic(contract, input);

      // Replace placeholders with params for a comparable string
      let resultQuery = sql_query;
      if(params) {
        let paramIndex = 0;
        resultQuery = sql_query.replace(/\?/g, () => `'${params[paramIndex++]}'`);
      }

      // Normalize SQL strings for comparison
      const normalize = (sql: string) => sql.replace(/\s+/g, ' ').replace(/;/g, '').trim();

      expect(normalize(resultQuery)).toBe(normalize(expectedSql));
    });
  });
});
