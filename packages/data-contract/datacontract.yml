# This file contains the semantic mappings and business rules for the legacy database schema.
# It is the single source of truth for the Model Context Protocol (MCP) server.
tables:
  CUST_MST:
    businessName: Customer Master
    description: Stores master information about customers.
    columns:
      c_id:
        businessName: Customer ID
        description: Unique identifier for a customer.
        dataType: INTEGER
        businessRules: []
      c_name:
        businessName: Customer Name
        description: Full name of the customer.
        dataType: VARCHAR(100)
        businessRules: []

  product_catalog:
    businessName: Product Catalog
    description: Stores information about all available products.
    columns:
      p_id:
        businessName: Product ID
        description: Unique identifier for a product.
        dataType: INTEGER
        businessRules: []
      p_name:
        businessName: Product Name
        description: Name of the product.
        dataType: VARCHAR(255)
        businessRules: []
      price:
        businessName: Price
        description: The selling price of the product.
        dataType: DECIMAL(10, 2)
        businessRules: []

  so_hdr:
    businessName: Sales Orders
    description: Stores header-level information for customer sales orders.
    columns:
      ord_id:
        businessName: Order ID
        description: Unique identifier for a sales order.
        dataType: INTEGER
        businessRules: []
      ord_stat:
        businessName: Order Status
        description: The current status of the sales order.
        dataType: SMALLINT
        businessRules:
          - "Maps integer codes to business meanings: 1: 'Pending', 2: 'Shipped', 3: 'Delivered', 4: 'Cancelled'"

  sales_transactions:
    businessName: Sales Transactions
    description: Stores line-item details for each sales transaction.
    columns:
      t_id:
        businessName: Transaction ID
        description: Unique identifier for a single transaction line item.
        dataType: INTEGER
        businessRules: []
      ord_id:
        businessName: Order ID
        description: Foreign key linking to the sales_order_header.
        dataType: INTEGER
        businessRules: []
      p_id:
        businessName: Product ID
        description: Foreign key linking to the product_catalog.
        dataType: INTEGER
        businessRules: []
      quantity:
        businessName: Quantity
        description: The number of units of the aproduct sold in this transaction.
        dataType: INTEGER
        businessRules: []

abbreviations:
  CUST: Customer
  MST: Master
  so: Sales Order
  hdr: Header
  ord: Order
  stat: Status
  p: Product

tools:
  explore_table_structure:
    description: "Get the detailed structure and business context for a specific table."
    readOnlyHint: true
    input_schema:
      type: object
      properties:
        tableName:
          type: string
          description: "The physical name of the table to explore (e.g., 'CUST_MST')."
      required:
        - tableName
    response_schema:
      type: object
      properties:
        businessName:
          type: string
        description:
          type: string
        columns:
          type: object
          additionalProperties:
            type: object
            properties:
              businessName:
                type: string
              description:
                type: string
              dataType:
                type: string
              businessRules:
                type: array
                items:
                  type: string
  explain_column_meaning:
    description: "Get the business definition and rules for a specific column in a table."
    readOnlyHint: true
    input_schema:
      type: object
      properties:
        tableName:
          type: string
          description: "The physical name of the table containing the column."
        columnName:
          type: string
          description: "The physical name of the column to explain."
      required:
        - tableName
        - columnName
    response_schema:
      type: object
      properties:
        businessName:
          type: string
        description:
          type: string
        businessRules:
          type: array
          items:
            type: string
  expand_abbreviations:
    description: "Expand a known business abbreviation or acronym into its full term."
    readOnlyHint: true
    input_schema:
      type: object
      properties:
        abbreviation:
          type: string
          description: "The abbreviation to expand (e.g., 'CUST')."
      required:
        - abbreviation
    response_schema:
      type: object
      properties:
        expansion:
          type: string
  build_query_from_description:
    description: "Build a safe and accurate SQL query from a natural language description of a data request."
    readOnlyHint: true
    input_schema:
      type: object
      properties:
        description:
          type: string
          description: "A natural language string describing the data to retrieve (e.g., 'Show me the total sales for customer Big Corp')."
      required:
        - description
    response_schema:
      type: object
      properties:
        sql_query:
          type: string
          description: "The generated SQL query."
